{"name": "justprototype-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^5.17.1", "@mui/material": "^5.17.1", "@types/uuid": "^9.0.8", "ajv": "^8.12.0", "ajv-formats": "^2.1.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.12.0", "react-router-dom": "^6.22.3", "uuid": "^9.0.1"}, "devDependencies": {"@types/ajv": "^1.0.0", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.5.3", "react-syntax-highlighter": "^15.5.0", "tailwindcss": "^3.4.17", "typescript": "^5.2.2", "vite": "^5.1.6"}}