/**
 * Production-ready EditorPageV3 - Refactored with modular architecture
 * Clean, maintainable, and scalable implementation
 */

import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useEditorV3, ChatMessage } from '../hooks/useEditorV3';
import ChatInterface from '../components/Editor/ChatInterface';
import PreviewPanel from '../components/Editor/PreviewPanel';
import PageManager from '../components/Editor/PageManager';

// ============================================================================
// TYPES
// ============================================================================

interface LocationState {
  prompt?: string;
  plan?: any;
  initialGeneration?: boolean;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function EditorPageV3Refactored() {
  const location = useLocation();
  const navigate = useNavigate();
  const { prompt, plan, initialGeneration } = (location.state as LocationState) || {};

  // Use the custom hook for all editor functionality
  const { state, actions, refs } = useEditorV3();

  // Additional state for robust UX
  const [showPageCreationDialog, setShowPageCreationDialog] = useState(false);
  const [pendingPageCreation, setPendingPageCreation] = useState<{
    pageName: string;
    pageId: string;
  } | null>(null);
  const [showLinkingDialog, setShowLinkingDialog] = useState(false);
  const [linkingProgress, setLinkingProgress] = useState<{
    current: number;
    total: number;
    currentPage: string;
  } | null>(null);

  // ============================================================================
  // INITIALIZATION EFFECTS
  // ============================================================================

  // Watch for page updates to show linking suggestion
  useEffect(() => {
    const pagesWithContent = state.pages.filter(p => p.content && p.content.length > 50);
    console.log('🔗 Page update detected:', {
      totalPages: state.pages.length,
      pagesWithContent: pagesWithContent.length,
      pages: state.pages.map(p => ({
        name: p.name,
        hasContent: p.content && p.content.length > 50,
        contentLength: p.content?.length || 0
      }))
    });

    // Show linking suggestion if we have 2+ pages with content and haven't shown it yet
    if (pagesWithContent.length >= 2 && !showLinkingDialog && !linkingProgress) {
      console.log('🔗 Conditions met for linking suggestion - showing dialog');
      // Use a longer delay to ensure state is fully updated
      setTimeout(() => {
        console.log('🔗 Actually showing linking dialog now');
        setShowLinkingDialog(true);
      }, 2000);
    }
  }, [state.pages]);

  // Handle initial generation from plan page
  useEffect(() => {
    if (initialGeneration && prompt && !state.htmlContent) {
      // Add user message
      const userMessage: ChatMessage = {
        role: 'user',
        content: prompt,
        timestamp: new Date()
      };
      actions.addMessage(userMessage);

      // Add plan message if we have a plan
      if (plan) {
        const planContent = formatPlanForDisplay(plan);
        const planMessage: ChatMessage = {
          role: 'assistant',
          content: planContent,
          timestamp: new Date(),
          type: 'plan'
        };
        actions.addMessage(planMessage);
      }

      // Start generation
      actions.generateFromPrompt(prompt);
    }
  }, [initialGeneration, prompt, state.htmlContent, plan, actions]);

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  const handleChatSubmit = async (message: string) => {
    // Add user message
    const userMessage: ChatMessage = {
      role: 'user',
      content: message,
      timestamp: new Date()
    };
    actions.addMessage(userMessage);
    actions.clearInput();

    // Determine if this is an edit or new generation
    const hasContent = state.htmlContent.length > 0 || state.stableIframeContent.length > 0;

    if (hasContent) {
      await actions.editContent(message);
    } else {
      await actions.generateFromPrompt(message);
    }
  };

  const handleElementClick = (element: any) => {
    console.log('🔥 Element clicked in iframe:', element);

    // Handle navigation clicks
    if (element.isNavigation) {
      console.log('🔥 Navigation detected, handling navigation click');
      handleNavigationClick(element);
    } else {
      // Handle other interactive elements
      console.log('🔥 Non-navigation element clicked');
      actions.setSelectedElement(element);
      actions.setShowImplementModal(true);
    }
  };

  const handleNavigationClick = async (element: any) => {
    const pageName = element.textContent.trim();
    const pageId = generatePageId(pageName);

    console.log('🔥 Processing navigation click:', { pageName, pageId });

    // Check if page already exists
    const existingPage = state.pages.find(p =>
      p.id === pageId ||
      p.name.toLowerCase().trim() === pageName.toLowerCase().trim()
    );

    if (existingPage) {
      console.log('🔥 Page already exists, switching to:', existingPage);
      actions.switchToPage(existingPage.id);

      // Add feedback message
      actions.addMessage({
        role: 'assistant',
        content: `✅ Switched to "${existingPage.name}" page`,
        timestamp: new Date()
      });
    } else {
      console.log('🔥 Page does not exist, showing creation dialog');

      // Show confirmation dialog instead of creating immediately
      setPendingPageCreation({ pageName, pageId });
      setShowPageCreationDialog(true);
    }
  };

  const confirmPageCreation = async () => {
    if (!pendingPageCreation) return;

    const { pageName, pageId } = pendingPageCreation;

    // Close dialog
    setShowPageCreationDialog(false);
    setPendingPageCreation(null);

    // Add feedback message
    actions.addMessage({
      role: 'assistant',
      content: `🚀 Creating new "${pageName}" page...`,
      timestamp: new Date()
    });

    // Create new page
    const newPage = {
      id: pageId,
      name: pageName,
      content: '',
      isActive: false
    };

    actions.addPage(newPage);
    actions.switchToPage(pageId);

    // Generate content for the new page
    const prompt = generatePagePrompt(pageName, state.pages);
    await actions.generateFromPrompt(prompt);

    // Linking suggestion will be shown automatically by the useEffect watching page updates
  };

  const cancelPageCreation = () => {
    setShowPageCreationDialog(false);
    setPendingPageCreation(null);

    actions.addMessage({
      role: 'assistant',
      content: `❌ Page creation cancelled`,
      timestamp: new Date()
    });
  };

  const handlePageSwitch = (pageId: string) => {
    actions.switchToPage(pageId);

    const page = state.pages.find(p => p.id === pageId);
    if (page) {
      actions.addMessage({
        role: 'assistant',
        content: `📄 Switched to "${page.name}" page`,
        timestamp: new Date()
      });
    }
  };

  const handlePageAdd = (page: any) => {
    actions.addPage(page);
    actions.switchToPage(page.id);

    actions.addMessage({
      role: 'assistant',
      content: `📄 Created new page "${page.name}". Describe what content you'd like on this page.`,
      timestamp: new Date()
    });
  };

  const handleLinkPages = async () => {
    const pagesWithContent = state.pages.filter(p => p.content && p.content.length > 0);

    if (pagesWithContent.length < 2) {
      actions.addMessage({
        role: 'assistant',
        content: '⚠️ Need at least 2 pages with content to link navigation',
        timestamp: new Date()
      });
      return;
    }

    // Close linking dialog if open
    setShowLinkingDialog(false);

    // Initialize progress tracking
    setLinkingProgress({
      current: 0,
      total: pagesWithContent.length,
      currentPage: ''
    });

    actions.addMessage({
      role: 'assistant',
      content: `🔗 Linking ${pagesWithContent.length} pages with navigation...`,
      timestamp: new Date()
    });

    try {
      // Use the improved linking with progress callback
      await linkPagesWithProgress(pagesWithContent);

      actions.addMessage({
        role: 'assistant',
        content: '✅ All pages have been linked with navigation!',
        timestamp: new Date()
      });
    } catch (error) {
      console.error('Linking failed:', error);
      actions.addMessage({
        role: 'assistant',
        content: '❌ Failed to link some pages. Please try again.',
        timestamp: new Date()
      });
    } finally {
      setLinkingProgress(null);
    }
  };

  const linkPagesWithProgress = async (pages: any[]) => {
    for (let i = 0; i < pages.length; i++) {
      const page = pages[i];

      // Update progress
      setLinkingProgress({
        current: i + 1,
        total: pages.length,
        currentPage: page.name
      });

      const otherPageNames = pages
        .filter(p => p.id !== page.id)
        .map(p => p.name);

      const prompt = `Update the navigation bar to include links to: ${otherPageNames.join(', ')}

🎯 REQUIREMENTS:
- Keep existing design exactly the same
- Add proper <a> tags for each page link
- Maintain responsive behavior
- Preserve all other content on the page`;

      try {
        const response = await fetch('/api/llm/v3/edit', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({
            htmlContent: page.content,
            prompt
          })
        });

        if (response.ok) {
          // Process the streaming response
          const reader = response.body?.getReader();
          if (reader) {
            const decoder = new TextDecoder();
            let accumulatedContent = '';
            let isCollectingHTML = false;

            while (true) {
              const { done, value } = await reader.read();
              if (done) break;

              const chunk = decoder.decode(value);
              const lines = chunk.split('\n');

              for (const line of lines) {
                if (line.startsWith('event:')) {
                  const event = line.substring(6);
                  if (event === 'start') {
                    isCollectingHTML = true;
                  } else if (event === 'end') {
                    isCollectingHTML = false;
                    // Update page content
                    const cleanHtml = extractHtmlFromResponse(accumulatedContent);
                    actions.updatePage(page.id, { content: cleanHtml });
                  }
                } else if (line.startsWith('data:') && isCollectingHTML) {
                  const data = line.substring(5);
                  accumulatedContent += data;
                }
              }
            }
          }
        }
      } catch (error) {
        console.error(`Failed to update page ${page.name}:`, error);
      }

      // Small delay between requests
      if (i < pages.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
  };

  const extractHtmlFromResponse = (response: string): string => {
    if (!response) return '';

    // Look for HTML content between ```html and ``` markers
    const htmlMatch = response.match(/```html\s*([\s\S]*?)\s*```/);
    if (htmlMatch) {
      return htmlMatch[1].trim();
    }

    // Look for HTML starting with DOCTYPE or html tag
    const doctypeMatch = response.match(/(<!DOCTYPE html[\s\S]*)/i);
    if (doctypeMatch) {
      return doctypeMatch[1].trim();
    }

    const htmlTagMatch = response.match(/(<html[\s\S]*)/i);
    if (htmlTagMatch) {
      return htmlTagMatch[1].trim();
    }

    // If response contains HTML tags, assume it's HTML
    if (response.includes('<') && response.includes('>')) {
      const firstTagIndex = response.indexOf('<');
      return response.substring(firstTagIndex).trim();
    }

    return response;
  };

  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================

  const formatPlanForDisplay = (plan: any): string => {
    if (typeof plan === 'string') {
      return plan;
    }

    if (plan && typeof plan === 'object') {
      let planContent = `📋 **Project Overview**\n${plan.overview || 'No overview provided'}\n\n`;

      if (plan.sections && Array.isArray(plan.sections)) {
        planContent += `🏗️ **Implementation Sections**\n`;
        plan.sections.forEach((section: any, index: number) => {
          planContent += `\n${index + 1}. **${section.title}**\n`;
          planContent += `   ${section.description}\n`;
          if (section.details && Array.isArray(section.details)) {
            section.details.forEach((detail: string) => {
              planContent += `   • ${detail}\n`;
            });
          }
        });
      }

      if (plan.features && Array.isArray(plan.features)) {
        planContent += `\n✨ **Key Features**\n`;
        plan.features.forEach((feature: string) => {
          planContent += `• ${feature}\n`;
        });
      }

      return planContent;
    }

    return '';
  };

  const generatePageId = (name: string): string => {
    return name.toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .replace(/^-+|-+$/g, '');
  };

  const generatePagePrompt = (pageName: string, existingPages: any[]): string => {
    const existingPageNames = existingPages
      .filter(p => p.name !== 'Main Page')
      .map(p => p.name);

    return `Create a complete, production-grade ${pageName} page with:

🎯 **CRITICAL REQUIREMENTS:**
1. **Full Navigation Bar** - Include links to ALL existing pages: Home/Main Page${existingPageNames.length > 0 ? ', ' + existingPageNames.join(', ') : ''}
2. **Consistent Design** - Match the exact style, colors, and layout of the main page
3. **Professional Content** - High-quality, relevant content for "${pageName}"
4. **Responsive Layout** - Mobile-friendly design
5. **Interactive Elements** - Buttons, forms, or features appropriate for this page

🔗 **Navigation Requirements:**
- Navigation bar at the top with links to: Home, ${existingPageNames.join(', ')}${existingPageNames.length > 0 ? ', ' : ''}About, Contact
- Each nav link should be clickable (use <a> tags with proper text)
- Current page ("${pageName}") should be highlighted/active in navigation
- Consistent navigation styling across all pages

📄 **Content for ${pageName}:**
${getPageContentGuidelines(pageName)}

Create a complete, standalone HTML page that looks professional and matches the main page design exactly.`;
  };

  const getPageContentGuidelines = (pageName: string): string => {
    const name = pageName.toLowerCase();

    if (name.includes('about')) {
      return '- Company/team information, mission, values, history, team members';
    } else if (name.includes('contact')) {
      return '- Contact form, address, phone, email, social media links, map';
    } else if (name.includes('services')) {
      return '- Service offerings, pricing, features, benefits';
    } else if (name.includes('sign up') || name.includes('signup') || name.includes('register')) {
      return '- Registration form with fields like name, email, password, confirm password\n- Clear call-to-action buttons\n- Terms of service and privacy policy links\n- Social login options if appropriate\n- "Already have an account? Login" link';
    } else if (name.includes('login') || name.includes('sign in') || name.includes('signin')) {
      return '- Login form with email/username and password fields\n- "Remember me" checkbox\n- "Forgot password?" link\n- "Don\'t have an account? Sign up" link\n- Social login options if appropriate';
    } else if (name.includes('pricing')) {
      return '- Pricing tiers with features comparison\n- Monthly/yearly toggle\n- "Most popular" highlighting\n- Clear call-to-action buttons\n- FAQ section about pricing';
    } else {
      return `- Relevant, professional content appropriate for "${pageName}"`;
    }
  };

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <div className="h-screen bg-gray-50 flex">
      {/* Page Manager Sidebar */}
      <div className="w-80 flex-shrink-0">
        <PageManager
          pages={state.pages}
          currentPageId={state.currentPageId}
          isLinking={!!linkingProgress}
          onPageSwitch={handlePageSwitch}
          onPageAdd={handlePageAdd}
          onPageUpdate={actions.updatePage}
          onLinkPages={() => {
            // Force show linking dialog for testing
            setShowLinkingDialog(true);
          }}
        />
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex">
        {/* Preview Panel */}
        <div className="flex-1">
          <PreviewPanel
            htmlContent={state.htmlContent}
            streamingContent={state.streamingContent}
            stableIframeContent={state.stableIframeContent}
            viewMode={state.viewMode}
            isGenerating={state.isGenerating}
            onViewModeChange={actions.setViewMode}
            onElementClick={handleElementClick}
          />
        </div>

        {/* Chat Interface */}
        <div className="w-96 flex-shrink-0">
          <ChatInterface
            messages={state.messages}
            input={state.input}
            isGenerating={state.isGenerating}
            onInputChange={actions.setInput}
            onSubmit={handleChatSubmit}
          />
        </div>
      </div>

      {/* Page Creation Confirmation Dialog */}
      {showPageCreationDialog && pendingPageCreation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Create New Page</h3>
            </div>

            <p className="text-gray-600 mb-2">
              You clicked on a navigation link for <strong>"{pendingPageCreation.pageName}"</strong>
            </p>
            <p className="text-sm text-gray-500 mb-6">
              This page doesn't exist yet. Would you like me to create it with relevant content?
            </p>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-6">
              <p className="text-sm text-blue-800">
                <strong>What I'll do:</strong>
              </p>
              <ul className="text-sm text-blue-700 mt-1 space-y-1">
                <li>• Create a new "{pendingPageCreation.pageName}" page</li>
                <li>• Generate relevant content for this page type</li>
                <li>• Maintain consistent design with your existing pages</li>
                <li>• Add proper navigation links</li>
              </ul>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={cancelPageCreation}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={confirmPageCreation}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Create Page
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Linking Suggestion Dialog */}
      {showLinkingDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.102m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Link Your Pages</h3>
            </div>

            <p className="text-gray-600 mb-2">
              Great! You now have multiple pages.
            </p>
            <p className="text-sm text-gray-500 mb-6">
              Would you like me to update the navigation on all pages so visitors can easily navigate between them?
            </p>

            <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-6">
              <p className="text-sm text-green-800">
                <strong>What I'll do:</strong>
              </p>
              <ul className="text-sm text-green-700 mt-1 space-y-1">
                <li>• Update navigation bars on all pages</li>
                <li>• Add links to all your pages</li>
                <li>• Maintain consistent styling</li>
                <li>• Preserve all existing content</li>
              </ul>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowLinkingDialog(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Maybe Later
              </button>
              <button
                onClick={handleLinkPages}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.102m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
                Link Pages
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Linking Progress Modal */}
      {linkingProgress && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-blue-600 animate-spin" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Linking Pages</h3>
            </div>

            <p className="text-gray-600 mb-4">
              Updating navigation on all pages...
            </p>

            <div className="mb-4">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>Progress</span>
                <span>{linkingProgress.current} of {linkingProgress.total}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(linkingProgress.current / linkingProgress.total) * 100}%` }}
                ></div>
              </div>
            </div>

            {linkingProgress.currentPage && (
              <p className="text-sm text-gray-500">
                Currently updating: <strong>{linkingProgress.currentPage}</strong>
              </p>
            )}
          </div>
        </div>
      )}

      {/* Implementation Modal */}
      {state.showImplementModal && state.selectedElement && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Implement Feature</h3>
            </div>

            <p className="text-gray-600 mb-2">
              You clicked on: <strong>{state.selectedElement.textContent}</strong>
            </p>
            <p className="text-sm text-gray-500 mb-6">
              Describe what you'd like this element to do, and I'll implement it for you.
            </p>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => actions.setShowImplementModal(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  actions.setShowImplementModal(false);
                  // Could add implementation logic here
                }}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                Implement
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default EditorPageV3Refactored;
