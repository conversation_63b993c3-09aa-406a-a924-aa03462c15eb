/**
 * Production-ready hook for EditorV3 core logic
 * Extracts and modularizes the main editor functionality
 */

import { useState, useRef, useCallback, useEffect } from 'react';

// ============================================================================
// TYPES
// ============================================================================

export type ViewMode = 'preview' | 'code';

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  type?: 'plan' | 'code' | 'message';
}

export interface Page {
  id: string;
  name: string;
  content: string;
  isActive: boolean;
  lastUpdated?: Date;
}

export interface ElementInfo {
  selector: string;
  tagName: string;
  textContent: string;
  attributes: Record<string, string>;
  isNavigation: boolean;
  isInteractive: boolean;
}

export interface EditorState {
  // Content state
  htmlContent: string;
  streamingContent: string;
  stableIframeContent: string;

  // UI state
  viewMode: ViewMode;
  isGenerating: boolean;
  isLinking: boolean;

  // Multi-page state
  pages: Page[];
  currentPageId: string;

  // Chat state
  messages: ChatMessage[];
  input: string;

  // Selection state
  selectedElement: ElementInfo | null;
  showImplementModal: boolean;
}

export interface EditorActions {
  // Content actions
  setHtmlContent: (content: string) => void;
  setStreamingContent: (content: string) => void;
  setStableIframeContent: (content: string) => void;

  // UI actions
  setViewMode: (mode: ViewMode) => void;
  setIsGenerating: (generating: boolean) => void;
  setIsLinking: (linking: boolean) => void;

  // Multi-page actions
  addPage: (page: Omit<Page, 'lastUpdated'>) => void;
  updatePage: (pageId: string, updates: Partial<Page>) => void;
  switchToPage: (pageId: string) => void;

  // Chat actions
  addMessage: (message: ChatMessage) => void;
  setInput: (input: string) => void;
  clearInput: () => void;

  // Selection actions
  setSelectedElement: (element: ElementInfo | null) => void;
  setShowImplementModal: (show: boolean) => void;

  // Core operations
  generateFromPrompt: (prompt: string) => Promise<void>;
  editContent: (prompt: string) => Promise<void>;
  linkAllPages: () => Promise<void>;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

const extractHtmlFromResponse = (response: string): string => {
  if (!response) return '';

  // Look for HTML content between ```html and ``` markers
  const htmlMatch = response.match(/```html\s*([\s\S]*?)\s*```/);
  if (htmlMatch) {
    return htmlMatch[1].trim();
  }

  // Look for HTML starting with DOCTYPE or html tag
  const doctypeMatch = response.match(/(<!DOCTYPE html[\s\S]*)/i);
  if (doctypeMatch) {
    return doctypeMatch[1].trim();
  }

  const htmlTagMatch = response.match(/(<html[\s\S]*)/i);
  if (htmlTagMatch) {
    return htmlTagMatch[1].trim();
  }

  // If response contains HTML tags, assume it's HTML
  if (response.includes('<') && response.includes('>')) {
    const firstTagIndex = response.indexOf('<');
    return response.substring(firstTagIndex).trim();
  }

  return response;
};

const addInteractionDetection = (html: string): string => {
  // Remove any existing scripts first
  let cleanHtml = html
    .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
    .replace(/<!-- INTERACTION_DETECTION_ADDED -->/g, '');

  const interactionScript = `
    <script>
      console.log('🔥 Interaction detection script loaded');

      // Add interaction detection after DOM is loaded
      document.addEventListener('DOMContentLoaded', function() {
        console.log('🔥 DOM loaded, setting up interaction detection');

        // Function to check if element is navigation
        function isNavigationElement(element) {
          const navSelectors = ['nav', 'header', '.nav', '.navbar', '.navigation', '.menu'];
          const tagName = element.tagName.toLowerCase();
          const className = element.className || '';
          const id = element.id || '';

          // Check if element or parent is navigation
          if (tagName === 'nav' || tagName === 'header') return true;
          if (className.includes('nav') || className.includes('menu')) return true;
          if (id.includes('nav') || id.includes('menu')) return true;

          // Check parent elements
          let parent = element.parentElement;
          while (parent && parent !== document.body) {
            const parentTag = parent.tagName.toLowerCase();
            const parentClass = parent.className || '';
            const parentId = parent.id || '';

            if (parentTag === 'nav' || parentTag === 'header') return true;
            if (parentClass.includes('nav') || parentClass.includes('menu')) return true;
            if (parentId.includes('nav') || parentId.includes('menu')) return true;

            parent = parent.parentElement;
          }

          return false;
        }

        // Add click listeners to all interactive elements
        document.addEventListener('click', function(event) {
          const element = event.target;
          console.log('🔥 Element clicked:', element);

          // Get element information
          const elementInfo = {
            tagName: element.tagName,
            textContent: element.textContent?.trim() || '',
            className: element.className || '',
            id: element.id || '',
            href: element.href || '',
            isNavigation: isNavigationElement(element),
            isInteractive: ['A', 'BUTTON', 'INPUT', 'SELECT', 'TEXTAREA'].includes(element.tagName),
            selector: getElementSelector(element)
          };

          console.log('🔥 Element info:', elementInfo);

          // Send message to parent window
          if (window.parent && window.parent !== window) {
            window.parent.postMessage({
              type: 'ELEMENT_CLICKED',
              element: elementInfo
            }, '*');
          }

          // Prevent default for navigation elements to avoid page navigation
          if (elementInfo.isNavigation || element.tagName === 'A') {
            event.preventDefault();
          }
        });

        function getElementSelector(element) {
          if (element.id) return '#' + element.id;
          if (element.className) {
            const classes = element.className.split(' ').filter(c => c.trim());
            if (classes.length > 0) return '.' + classes[0];
          }
          return element.tagName.toLowerCase();
        }
      });
    </script>
    <!-- INTERACTION_DETECTION_ADDED -->
  `;

  // Insert script before closing body tag, or at the end if no body tag
  if (cleanHtml.includes('</body>')) {
    cleanHtml = cleanHtml.replace('</body>', `${interactionScript}\n</body>`);
  } else {
    cleanHtml += interactionScript;
  }

  return cleanHtml;
};

const ensureCompleteHtml = (content: string): string => {
  if (!content.includes('<!DOCTYPE html')) {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated Content</title>
</head>
<body>
    ${content}
</body>
</html>`;
  }
  return content;
};

// ============================================================================
// MAIN HOOK
// ============================================================================

export function useEditorV3() {
  // ============================================================================
  // STATE
  // ============================================================================

  const [state, setState] = useState<EditorState>({
    // Content state
    htmlContent: '',
    streamingContent: '',
    stableIframeContent: '',

    // UI state
    viewMode: 'preview',
    isGenerating: false,
    isLinking: false,

    // Multi-page state
    pages: [
      { id: 'main', name: 'Main Page', content: '', isActive: true, lastUpdated: new Date() }
    ],
    currentPageId: 'main',

    // Chat state
    messages: [
      {
        role: 'assistant',
        content: 'Hi! I\'m your AI design assistant. Describe what you\'d like to create and I\'ll build it for you.',
        timestamp: new Date()
      }
    ],
    input: '',

    // Selection state
    selectedElement: null,
    showImplementModal: false
  });

  const iframeRef = useRef<HTMLIFrameElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // ============================================================================
  // ACTIONS
  // ============================================================================

  const updateState = useCallback((updates: Partial<EditorState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const setHtmlContent = useCallback((content: string) => {
    updateState({ htmlContent: content });
  }, [updateState]);

  const setStreamingContent = useCallback((content: string) => {
    updateState({ streamingContent: content });
  }, [updateState]);

  const setStableIframeContent = useCallback((content: string) => {
    updateState({ stableIframeContent: content });
  }, [updateState]);

  const setViewMode = useCallback((mode: ViewMode) => {
    updateState({ viewMode: mode });
  }, [updateState]);

  const setIsGenerating = useCallback((generating: boolean) => {
    updateState({ isGenerating: generating });
  }, [updateState]);

  const setIsLinking = useCallback((linking: boolean) => {
    updateState({ isLinking: linking });
  }, [updateState]);

  const addPage = useCallback((page: Omit<Page, 'lastUpdated'>) => {
    const newPage = { ...page, lastUpdated: new Date() };
    setState(prev => ({
      ...prev,
      pages: [...prev.pages, newPage]
    }));
  }, []);

  const updatePage = useCallback((pageId: string, updates: Partial<Page>) => {
    setState(prev => ({
      ...prev,
      pages: prev.pages.map(page =>
        page.id === pageId
          ? { ...page, ...updates, lastUpdated: new Date() }
          : page
      )
    }));
  }, []);

  const switchToPage = useCallback((pageId: string) => {
    setState(prev => {
      const page = prev.pages.find(p => p.id === pageId);
      if (page) {
        return {
          ...prev,
          currentPageId: pageId,
          htmlContent: extractHtmlFromResponse(page.content),
          stableIframeContent: page.content
        };
      }
      return prev;
    });
  }, []);

  const addMessage = useCallback((message: ChatMessage) => {
    setState(prev => ({
      ...prev,
      messages: [...prev.messages, message]
    }));
  }, []);

  const setInput = useCallback((input: string) => {
    updateState({ input });
  }, [updateState]);

  const clearInput = useCallback(() => {
    updateState({ input: '' });
  }, [updateState]);

  const setSelectedElement = useCallback((element: ElementInfo | null) => {
    updateState({ selectedElement: element });
  }, [updateState]);

  const setShowImplementModal = useCallback((show: boolean) => {
    updateState({ showImplementModal: show });
  }, [updateState]);

  // ============================================================================
  // CORE OPERATIONS
  // ============================================================================

  const generateFromPrompt = useCallback(async (prompt: string) => {
    setIsGenerating(true);
    setStreamingContent('');

    try {
      const response = await fetch('/api/llm/v3/generate-html', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ prompt })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) throw new Error('No response body');

      let accumulatedContent = '';
      let isCollectingHTML = false;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = new TextDecoder().decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('event:')) {
            const event = line.substring(6);
            if (event === 'start') {
              isCollectingHTML = true;
            } else if (event === 'end') {
              isCollectingHTML = false;
              const cleanHtml = extractHtmlFromResponse(accumulatedContent);
              setHtmlContent(cleanHtml);
              setStreamingContent('');
            }
          } else if (line.startsWith('data:') && isCollectingHTML) {
            const data = line.substring(5);
            accumulatedContent += data;
            setStreamingContent(accumulatedContent);
          }
        }
      }

      addMessage({
        role: 'assistant',
        content: 'I\'ve created your design! What would you like to modify?',
        timestamp: new Date()
      });

    } catch (error) {
      console.error('Generation error:', error);
      addMessage({
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date()
      });
    } finally {
      setIsGenerating(false);
    }
  }, [setIsGenerating, setStreamingContent, setHtmlContent, addMessage]);

  const editContent = useCallback(async (prompt: string) => {
    const currentContent = state.htmlContent || state.stableIframeContent;
    if (!currentContent) {
      await generateFromPrompt(prompt);
      return;
    }

    setIsGenerating(true);
    setStreamingContent('');

    try {
      const response = await fetch('/api/llm/v3/edit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          htmlContent: currentContent,
          prompt
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) throw new Error('No response body');

      let accumulatedContent = '';
      let isCollectingHTML = false;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = new TextDecoder().decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('event:')) {
            const event = line.substring(6);
            if (event === 'start') {
              isCollectingHTML = true;
            } else if (event === 'end') {
              isCollectingHTML = false;
              const cleanHtml = extractHtmlFromResponse(accumulatedContent);
              setHtmlContent(cleanHtml);
              setStreamingContent('');
            }
          } else if (line.startsWith('data:') && isCollectingHTML) {
            const data = line.substring(5);
            accumulatedContent += data;
            setStreamingContent(accumulatedContent);
          }
        }
      }

      addMessage({
        role: 'assistant',
        content: 'I\'ve updated your design. What would you like to change next?',
        timestamp: new Date()
      });

    } catch (error) {
      console.error('Edit error:', error);
      addMessage({
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date()
      });
    } finally {
      setIsGenerating(false);
    }
  }, [state.htmlContent, state.stableIframeContent, generateFromPrompt, setIsGenerating, setStreamingContent, setHtmlContent, addMessage]);

  const linkAllPages = useCallback(async () => {
    const pagesWithContent = state.pages.filter(p => p.content && p.content.length > 0);
    if (pagesWithContent.length < 2) return;

    setIsLinking(true);

    try {
      for (const page of pagesWithContent) {
        const otherPageNames = pagesWithContent
          .filter(p => p.id !== page.id)
          .map(p => p.name);

        const prompt = `Update the navigation bar to include links to: ${otherPageNames.join(', ')}

🎯 REQUIREMENTS:
- Keep existing design exactly the same
- Add proper <a> tags for each page link
- Maintain responsive behavior
- Preserve all other content`;

        const response = await fetch('/api/llm/v3/edit', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({
            htmlContent: page.content,
            prompt
          })
        });

        if (response.ok) {
          // Process response and update page
          // Implementation similar to editContent but for specific page
        }

        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } catch (error) {
      console.error('Linking error:', error);
    } finally {
      setIsLinking(false);
    }
  }, [state.pages, setIsLinking]);

  // ============================================================================
  // EFFECTS
  // ============================================================================

  // Update stable iframe content when generation completes
  useEffect(() => {
    const cleanContent = extractHtmlFromResponse(state.htmlContent);
    if (cleanContent && !state.isGenerating && cleanContent.length > 50) {
      const completeHtml = ensureCompleteHtml(cleanContent);
      const interactiveHtml = addInteractionDetection(completeHtml);

      console.log('🔥 Generation complete - updating content for page:', state.currentPageId);
      console.log('🔥 Content length:', interactiveHtml.length);

      // Update both iframe and page content atomically
      setState(prev => ({
        ...prev,
        stableIframeContent: interactiveHtml,
        pages: prev.pages.map(page =>
          page.id === prev.currentPageId
            ? { ...page, content: interactiveHtml, lastUpdated: new Date() }
            : page
        )
      }));
    }
  }, [state.htmlContent, state.isGenerating, state.currentPageId]);

  // Auto-scroll messages
  useEffect(() => {
    if (!state.isGenerating) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [state.messages, state.isGenerating]);

  // ============================================================================
  // RETURN
  // ============================================================================

  const actions: EditorActions = {
    setHtmlContent,
    setStreamingContent,
    setStableIframeContent,
    setViewMode,
    setIsGenerating,
    setIsLinking,
    addPage,
    updatePage,
    switchToPage,
    addMessage,
    setInput,
    clearInput,
    setSelectedElement,
    setShowImplementModal,
    generateFromPrompt,
    editContent,
    linkAllPages
  };

  return {
    state,
    actions,
    refs: {
      iframeRef,
      messagesEndRef
    },
    utils: {
      extractHtmlFromResponse,
      addInteractionDetection,
      ensureCompleteHtml
    }
  };
}
