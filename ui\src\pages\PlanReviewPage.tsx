import { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Button } from '../components/Button';
import { streamFeaturePlan, getPrototypeQuota } from '../services/planService';
import { FiEdit2, FiPlus, FiRefreshCw } from 'react-icons/fi';
import useQuotaCheck from '../hooks/useQuotaCheck';
import QuotaExceededModal from '../components/QuotaExceededModal';
import styles from './PlanReviewPage.module.css';

type Feature = {
  id: number;
  text: string;
  checked: boolean;
  editing: boolean;
};

function isSectionHeading(line: string) {
  // Heuristic: heading if all uppercase, or matches "1. Layout", "2. Components", etc.
  return (
    /^[A-Z][A-Za-z\s]+$/.test(line) ||
    /^\d+\.\s*[A-Za-z\s]+$/.test(line)
  );
}

// Remove markdown formatting from plan lines
function cleanPlanLine(line: string): string {
  return line
    .replace(/^(\*\*|#+|\-+\s*|---+|\s*[*_]{1,3})/g, '') // leading markdown
    .replace(/(\*\*|#+|\-+|\s*[*_]{1,3})$/g, '') // trailing markdown
    .replace(/^(\s*[-*]\s+)/, '') // leading bullet
    .replace(/^\s*>\s*/, '') // blockquote
    .replace(/^\s*_{2,}\s*$/, '') // horizontal rule
    .replace(/^\s*---+\s*$/, '')
    .replace(/^\s*$/, '')
    .trim();
}

export function PlanReviewPage() {
  const location = useLocation();
  const navigate = useNavigate();
  const prompt: string = location.state?.prompt || '';
  const [features, setFeatures] = useState<Feature[]>([]);
  const [streaming, setStreaming] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const nextId = useRef(1);

  // Extract a sensible title from the initial prompt (first non-empty line, max 60 chars)
  const extractTitleFromPrompt = (prompt: string) => {
    if (!prompt) return 'Untitled Prototype';
    const firstLine = prompt.split('\n').find(line => line.trim())?.trim() || 'Untitled Prototype';
    return firstLine.length > 60 ? firstLine.slice(0, 60) + '...' : firstLine;
  };
  const prototypeTitle = extractTitleFromPrompt(prompt);

  // Use the quota check hook
  const { quota, isQuotaExceeded, refetchQuota } = useQuotaCheck();
  const [quotaModalOpen, setQuotaModalOpen] = useState(false);

  useEffect(() => {
    if (!prompt) return;
    setFeatures([]);
    setStreaming(true);
    setError(null);

    let buffer = '';

    (async () => {
      try {
        let lastHeading = '';
        for await (const chunk of streamFeaturePlan(prompt)) {
          buffer += chunk;
          let lines = buffer.split('\n');
          buffer = lines.pop() || '';
          setFeatures(prev => {
            const newFeatures: Feature[] = [];
            for (let line of lines.map(l => cleanPlanLine(l.trim())).filter(Boolean)) {
              // Filter out consecutive duplicate headings
              if (isSectionHeading(line)) {
                if (line.toLowerCase() === lastHeading.toLowerCase()) continue;
                lastHeading = line;
              }
              newFeatures.push({
                id: nextId.current++,
                text: line,
                checked: true,
                editing: false,
              });
            }
            return prev.concat(newFeatures);
          });
        }
        // Flush any remaining buffer
        if (buffer.trim()) {
          const cleaned = cleanPlanLine(buffer.trim());
          if (cleaned) {
            setFeatures(prev => [
              ...prev,
              {
                id: nextId.current++,
                text: cleaned,
                checked: true,
                editing: false,
              },
            ]);
          }
        }
      } catch (err: any) {
        console.error('Error streaming plan:', err);

        // Check if this is a quota exceeded error
        if (err.message && err.message.includes('QUOTA_EXCEEDED')) {
          setError('Quota exceeded: You\'ve reached your prototype limit. Please upgrade your plan to continue.');
          // Show the quota modal
          setQuotaModalOpen(true);
        } else {
          setError('Failed to stream plan. Please try again.');
        }
      } finally {
        setStreaming(false);
      }
    })();

    // Cleanup function
    return () => {
      // Nothing to clean up
    };
    // eslint-disable-next-line
  }, [prompt]);

  const [newFeature, setNewFeature] = useState('');
  const [adding, setAdding] = useState(false);

  const handleCheck = (id: number) => {
    setFeatures(f =>
      f.map(feat => feat.id === id ? { ...feat, checked: !feat.checked } : feat)
    );
  };

  const handleEdit = (id: number) => {
    setFeatures(f =>
      f.map(feat => feat.id === id ? { ...feat, editing: true } : { ...feat, editing: false })
    );
  };

  const handleEditChange = (id: number, value: string) => {
    setFeatures(f =>
      f.map(feat => feat.id === id ? { ...feat, text: value } : feat)
    );
  };

  const handleEditDone = (id: number) => {
    setFeatures(f =>
      f.map(feat => feat.id === id ? { ...feat, editing: false } : feat)
    );
  };

  const handleAddFeature = () => {
    if (!newFeature.trim()) return;
    setFeatures(f => [
      ...f,
      {
        id: Date.now(),
        text: newFeature,
        checked: true,
        editing: false,
      },
    ]);
    setNewFeature('');
    setAdding(false);
  };

  const handleRegenerate = () => {
    window.location.reload();
  };

  const handleProceed = async () => {
    // Check quota before proceeding
    await refetchQuota();

    if (isQuotaExceeded) {
      // Show quota exceeded modal
      setQuotaModalOpen(true);
      return;
    }

    // 1. Collect all checked features (including custom)
    // 2. Flatten into plain-text plan (one per line)
    // 3. Navigate to /editor with the plan
    const checkedFeatures = features.filter(f => f.checked && f.text.trim());
    const planText = checkedFeatures.map(f => f.text.trim()).join('\n');
    // Pass both plan and prototypeTitle to the editor
    navigate('/editor', { state: { plan: planText, prototypeTitle } });
  };

  const canProceed = features.some(f => f.checked);

  return (
    <div className={styles.planReviewPage}>
      {/* Show the prototype title extracted from the initial prompt */}
      <h2 className={styles.heading}>Review & Edit Your Prototype Plan</h2>
      <div className={styles.prototypeTitle} style={{fontWeight:600, fontSize:'1.2em', marginBottom:12}}>
        {prototypeTitle}
      </div>
      {error && <div className={styles.error}>{error}</div>}
      <div className={styles.featureList}>
        {features.map((feat) => {
          const isHeading = isSectionHeading(feat.text);
          return (
            <div
              className={`${styles.featureItem} ${feat.editing ? styles.editing : ''} ${isHeading ? styles.sectionHeading : ''}`}
              key={feat.id}
              style={isHeading ? { fontWeight: 600, fontSize: '1em', marginTop: 8, marginBottom: 2 } : {}}
            >
              {!isHeading && (
                <input
                  type="checkbox"
                  checked={feat.checked}
                  onChange={() => handleCheck(feat.id)}
                  className={styles.checkbox}
                />
              )}
              {feat.editing ? (
                <input
                  className={styles.featureInput}
                  value={feat.text}
                  onChange={e => handleEditChange(feat.id, e.target.value)}
                  onBlur={() => handleEditDone(feat.id)}
                  onKeyDown={e => e.key === 'Enter' && handleEditDone(feat.id)}
                  autoFocus
                />
              ) : (
                <span className={styles.featureText}>{feat.text}</span>
              )}
              {!isHeading && (
                <button
                  className={styles.editBtn}
                  onClick={() => handleEdit(feat.id)}
                  aria-label="Edit feature"
                  tabIndex={0}
                >
                  <FiEdit2 />
                </button>
              )}
            </div>
          );
        })}
        {adding ? (
          <div className={`${styles.featureItem} ${styles.adding}`}>
            <input
              className={styles.featureInput}
              value={newFeature}
              onChange={e => setNewFeature(e.target.value)}
              onBlur={handleAddFeature}
              onKeyDown={e => e.key === 'Enter' && handleAddFeature()}
              placeholder="Describe new feature"
              autoFocus
            />
          </div>
        ) : (
          <button
            className={styles.addBtn}
            onClick={() => setAdding(true)}
            aria-label="Add new feature"
          >
            <FiPlus /> Add New Feature
          </button>
        )}
      </div>
      <div className={styles.actions}>
        <Button
          variant="secondary"
          size="medium"
          animated
          className={styles.regenBtn}
          onClick={handleRegenerate}
          disabled={streaming}
        >
          <FiRefreshCw style={{ marginRight: 6 }} />
          Regenerate Plan
        </Button>
        <Button
          size="large"
          animated
          className={styles.proceedBtn}
          disabled={!canProceed || streaming}
          onClick={handleProceed}
        >
          Proceed
        </Button>
      </div>
      {streaming && <div className={styles.streamingNotice}>Streaming plan...</div>}

      {/* Quota Exceeded Modal */}
      <QuotaExceededModal
        open={quotaModalOpen}
        onClose={() => setQuotaModalOpen(false)}
        onUpgrade={() => {
          // Open payment page in a new tab
          window.open("https://justprototype.lemonsqueezy.com/buy/561b5e04-cb9f-4bee-9ed5-a99d8b6cc6fa", '_blank', 'noopener,noreferrer');
          setQuotaModalOpen(false);
        }}
        quota={quota}
      />
    </div>
  );
}
