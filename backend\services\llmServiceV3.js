const { ChatOpenAI } = require('@langchain/openai');
const { ChatAnthropic } = require('@langchain/anthropic');
const { HumanMessage, SystemMessage } = require('@langchain/core/messages');

// System prompts for different tasks
const systemPrompts = {
  intent: `You are a UI/UX expert. Analyze HTML elements and determine what functionality should be implemented when users interact with them.

Return a JSON response with:
- canGenerate: boolean (whether functionality can be generated)
- userIntent: string (what the user likely wants to achieve)
- suggestion: string (detailed implementation suggestion)

Focus on common UI patterns and user expectations.`,

  modification: `You are an elite frontend developer specializing in precise HTML modifications while maintaining production-grade quality.

CRITICAL INSTRUCTIONS:
1. Make ONLY the changes requested in the prompt
2. Preserve ALL existing functionality, styling, and structure
3. Maintain the exact same design system and patterns
4. Keep all unrelated elements completely unchanged
5. If adding new elements, follow existing patterns in the document
6. Ensure changes are contextually appropriate and well-integrated
7. Maintain clean, properly formatted code structure
8. Preserve consistent naming conventions and CSS organization
9. Keep the same visual design language and component styling

Return the complete modified HTML document without markdown formatting. Ensure the output maintains the same professional quality and code structure as the original.`,

  code: `You are an expert web developer. Create complete, modern, responsive HTML pages with embedded CSS and JavaScript.

REQUIREMENTS:
- Use modern HTML5 semantic elements
- Include responsive design with CSS Grid/Flexbox
- Add interactive JavaScript functionality
- Use modern CSS features (custom properties, etc.)
- Ensure accessibility (ARIA labels, semantic HTML)
- Include proper meta tags and viewport settings
- Use a cohesive design system with consistent colors and typography

Return only the complete HTML document without markdown formatting.`
};

/**
 * V3 LLM Service - Clean implementation based on Readdy.ai approach
 * Focuses on sophisticated prompting and context management for accurate editing
 */

class LLMServiceV3 {
  constructor() {
    this.providers = {
      openai: process.env.OPENAI_API_KEY,
      anthropic: process.env.ANTHROPIC_API_KEY
    };
  }

  /**
   * Create LLM instance based on provider
   */
  createLLM(provider = 'openai', streaming = true) {
    const providerKey = provider.toLowerCase();

    if (!this.providers[providerKey]) {
      throw new Error(`Provider ${provider} not configured or API key missing`);
    }

    switch (providerKey) {
      case 'openai':
        return new ChatOpenAI({
          apiKey: this.providers.openai,
          modelName: 'gpt-4',
          temperature: 0.7,
          streaming
        });

      case 'anthropic':
        return new ChatAnthropic({
          apiKey: this.providers.anthropic,
          modelName: 'claude-3-sonnet-20240229',
          temperature: 0.7,
          streaming
        });

      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }

  /**
   * Send SSE event
   */
  sendSSEEvent(res, event, data) {
    res.write(`event:${event}\n`);
    res.write(`data:${data}\n\n`);
  }

  /**
   * Generate intent from element code (Readdy.ai approach)
   */
  async generateIntent(elementCode) {
    const llm = this.createLLM('openai', false);

    const systemPrompt = `You are a UI/UX expert. Analyze HTML elements and determine what functionality should be implemented when users interact with them.

Return a JSON response with:
- canGenerate: boolean (whether functionality can be generated)
- userIntent: string (what the user likely wants to achieve)
- suggestion: string (detailed implementation suggestion)

Focus on common UI patterns and user expectations.`;

    const userPrompt = `Analyze this UI element:

${elementCode}

What functionality should be implemented when a user interacts with this element?`;

    const messages = [
      new SystemMessage(systemPrompt),
      new HumanMessage(userPrompt)
    ];

    try {
      const response = await llm.invoke(messages);
      return JSON.parse(response.content);
    } catch (error) {
      console.error('Error generating intent:', error);
      return {
        canGenerate: false,
        userIntent: 'Unable to determine user intent',
        suggestion: 'Please provide more context or try a different approach'
      };
    }
  }

  /**
   * Generate complete HTML from prompt
   */
  async generateHTML(prompt, res, provider = 'openai') {
    const llm = this.createLLM(provider, true);

    const systemPrompt = `You are an elite frontend developer creating production-grade HTML prototypes. Generate complete, functional web interfaces that implement ALL requested features comprehensively.

## CRITICAL REQUIREMENTS:

### Feature Implementation:
- Implement EVERY feature mentioned in the prompt completely
- Add functional JavaScript for all interactive elements
- Include working forms, buttons, navigation, and dynamic content
- Create realistic data and content for demonstrations
- Ensure all components are fully functional, not just visual

### Code Quality Standards:
- Write clean, semantic HTML5 with proper document structure
- Use modern CSS with custom properties (CSS variables)
- Implement responsive design with mobile-first approach
- Follow accessibility best practices (ARIA labels, semantic elements)
- Use consistent naming conventions (BEM methodology preferred)
- Include proper meta tags and viewport configuration

### Design System:
- Typography: Use system fonts with proper hierarchy (2.5rem, 2rem, 1.5rem, 1.25rem, 1rem)
- Colors: Professional palette with primary (#2563eb), secondary (#10b981), neutral grays
- Spacing: Consistent 8px grid system (0.5rem, 1rem, 1.5rem, 2rem, 3rem, 4rem)
- Components: Clean buttons, cards, forms with subtle shadows and proper states
- Layout: CSS Grid for page structure, Flexbox for components

### Functionality Requirements:
- Add working JavaScript for all interactive features
- Include form validation and submission handling
- Implement navigation and routing where applicable
- Add realistic sample data and content
- Create working animations and transitions
- Include error handling and user feedback

### Code Structure:
- Properly formatted and indented HTML
- Organized CSS with logical grouping (reset, variables, layout, components)
- Comprehensive JavaScript for all interactions
- Comments for complex sections
- No external dependencies unless absolutely necessary

### Visual Excellence:
- Modern, clean aesthetic with generous whitespace
- Subtle animations and hover effects
- Professional color schemes and typography
- Consistent component styling
- Mobile-responsive design

CRITICAL: Return ONLY the complete HTML document with embedded CSS and JavaScript.

DO NOT include:
- Any explanatory text
- Any comments about the code
- Any instructions to the user
- Any markdown formatting
- Any text outside the HTML document

Return ONLY clean HTML code that starts with <!DOCTYPE html> and ends with </html>. No other text whatsoever.`;

    const messages = [
      new SystemMessage(systemPrompt),
      new HumanMessage(prompt)
    ];

    try {
      this.sendSSEEvent(res, 'start', 'Starting HTML generation...');

      const stream = await llm.stream(messages);

      for await (const chunk of stream) {
        if (chunk.content) {
          this.sendSSEEvent(res, 'data', chunk.content);
        }
      }

      this.sendSSEEvent(res, 'end', 'HTML generation completed');
      res.end();
    } catch (error) {
      console.error('Error generating HTML:', error);
      this.sendSSEEvent(res, 'error', error.message);
      res.end();
    }
  }

  /**
   * Edit existing HTML with targeted changes (Readdy.ai approach)
   */
  async editHTML(htmlContent, prompt, res, provider = 'openai', elementSelector = null) {
    const llm = this.createLLM(provider, true);

    const systemPrompt = `You are an expert web developer specializing in precise HTML modifications. You excel at making targeted changes while preserving existing functionality.

CRITICAL INSTRUCTIONS:
1. Make ONLY the changes requested in the prompt
2. Preserve ALL existing functionality, styling, and structure
3. Maintain the exact same design system and patterns
4. Keep all unrelated elements completely unchanged
5. If adding new elements, follow existing patterns in the document
6. Ensure changes are contextually appropriate and well-integrated
7. If adding new features, implement them completely with working JavaScript
8. Maintain consistent code quality and formatting
9. Preserve all existing CSS variables, classes, and styling
10. Test that all existing functionality still works after changes

MODAL IMPLEMENTATION REQUIREMENTS (if creating modals):
- Add onclick="openModal('modalId')" directly to the button element
- Create modal HTML structure with unique ID using this EXACT pattern:
  <div id="modalId" class="hidden fixed inset-0 bg-black/50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg w-full max-w-lg mx-4">
      <div class="p-6 border-b border-gray-100">
        <div class="flex justify-between items-center">
          <h3 class="text-xl font-semibold">Modal Title</h3>
          <button onclick="closeModal('modalId')" class="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100">
            <i class="ri-close-line text-gray-500"></i>
          </button>
        </div>
      </div>
      <div class="p-6">
        <!-- Modal content here -->
      </div>
    </div>
  </div>

- Add these EXACT JavaScript functions:
  function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
  }
  function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
  }
  // ESC key listener
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      document.querySelectorAll('.modal:not(.hidden)').forEach(modal => modal.classList.add('hidden'));
    }
  });

- Use vanilla JavaScript only (no external libraries required)
- Follow the Readdy.ai modal pattern exactly

JAVASCRIPT IMPLEMENTATION STANDARDS:
- Use vanilla JavaScript only (no jQuery or external libraries)
- Add event handlers directly as onclick attributes for reliability
- Include complete function definitions in <script> tags
- Test all functionality before returning code
- Ensure immediate functionality after implementation

MODIFICATION APPROACH:
- Analyze the existing code structure and patterns
- Identify the specific area that needs modification
- Make precise, surgical changes to that area only
- Ensure new code follows the same patterns as existing code
- Maintain all existing functionality and styling
- Add comprehensive functionality for any new features

CRITICAL: Return ONLY the complete modified HTML document.

DO NOT include:
- Any explanatory text
- Any comments about changes made
- Any instructions to the user
- Any markdown formatting
- Any text outside the HTML document

Return ONLY clean HTML code that starts with <!DOCTYPE html> and ends with </html>. No other text whatsoever.`;

    let userPrompt = `CURRENT HTML DOCUMENT:
${htmlContent}

REQUESTED CHANGE: ${prompt}`;

    if (elementSelector) {
      userPrompt += `\n\nTARGET ELEMENT SELECTOR: ${elementSelector}
Focus changes on this specific element while preserving everything else.`;
    }

    const messages = [
      new SystemMessage(systemPrompt),
      new HumanMessage(userPrompt)
    ];

    try {
      this.sendSSEEvent(res, 'start', 'Starting HTML editing...');

      const stream = await llm.stream(messages);

      for await (const chunk of stream) {
        if (chunk.content) {
          this.sendSSEEvent(res, 'data', chunk.content);
        }
      }

      this.sendSSEEvent(res, 'end', 'HTML editing completed');
      res.end();
    } catch (error) {
      console.error('Error editing HTML:', error);
      this.sendSSEEvent(res, 'error', error.message);
      res.end();
    }
  }

  /**
   * Generate structured plan from prompt (for plan review page)
   */
  async generateStructuredPlan(prompt, deviceType = 'desktop', provider = 'openai') {
    const llm = this.createLLM(provider, false);

    const systemPrompt = `You are a senior web developer and UI/UX designer. Create detailed implementation plans for web applications.

Return a JSON object with this exact structure:
{
  "overview": "Brief description of the overall design approach",
  "sections": [
    {
      "title": "Section Name",
      "description": "What this section does",
      "details": ["Specific implementation detail 1", "Detail 2", "etc"]
    }
  ],
  "features": ["Key feature 1", "Feature 2", "etc"],
  "accessibility": ["Accessibility feature 1", "Feature 2", "etc"]
}

Focus on modern web design principles, responsive design, and user experience.`;

    const userPrompt = `Create a detailed implementation plan for: "${prompt}"

Device Type: ${deviceType}

Provide a comprehensive plan that covers layout, components, features, and accessibility considerations.`;

    const messages = [
      new SystemMessage(systemPrompt),
      new HumanMessage(userPrompt)
    ];

    try {
      const response = await llm.invoke(messages);

      // Try to parse JSON response
      try {
        const plan = JSON.parse(response.content);
        return { success: true, plan };
      } catch (parseError) {
        console.error('JSON parse error:', parseError);
        // Fallback plan if JSON parsing fails
        return {
          success: true,
          plan: {
            overview: "A modern, responsive design focused on user experience and accessibility.",
            sections: [
              {
                title: "Layout Structure",
                description: "Overall page layout and organization",
                details: ["Responsive grid system", "Mobile-first approach", "Clear visual hierarchy"]
              },
              {
                title: "User Interface",
                description: "Interactive elements and user experience",
                details: ["Intuitive navigation", "Clear call-to-action buttons", "Consistent styling"]
              }
            ],
            features: ["Responsive design", "Modern styling", "Interactive elements"],
            accessibility: ["Semantic HTML", "Keyboard navigation", "Screen reader support"]
          }
        };
      }
    } catch (error) {
      console.error('Error generating structured plan:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Generate plan from prompt (streaming for chat)
   */
  async generatePlan(prompt, res, provider = 'openai') {
    const llm = this.createLLM(provider, true);

    const systemPrompt = `You are a senior web developer and UI/UX designer. Create detailed implementation plans for web applications.

Your plan should include:
1. Overall structure and layout
2. Key components and features
3. User interface design approach
4. Interactive elements and functionality
5. Technical considerations
6. Implementation steps

Be specific and actionable.`;

    const messages = [
      new SystemMessage(systemPrompt),
      new HumanMessage(`Create a detailed plan for: ${prompt}`)
    ];

    try {
      this.sendSSEEvent(res, 'start', 'Starting plan generation...');

      const stream = await llm.stream(messages);

      for await (const chunk of stream) {
        if (chunk.content) {
          this.sendSSEEvent(res, 'data', chunk.content);
        }
      }

      this.sendSSEEvent(res, 'end', 'Plan generation completed');
      res.end();
    } catch (error) {
      console.error('Error generating plan:', error);
      this.sendSSEEvent(res, 'error', error.message);
      res.end();
    }
  }

  /**
   * Generate code from plan
   */
  async generateCode(plan, res, provider = 'openai') {
    const llm = this.createLLM(provider, true);

    const systemPrompt = `You are an elite frontend developer converting detailed plans into production-grade HTML prototypes. Create clean, semantic, and visually stunning web interfaces that match the quality of modern design tools.

## CRITICAL REQUIREMENTS:

### Code Quality Standards:
- Write clean, semantic HTML5 with proper document structure
- Use modern CSS with custom properties (CSS variables)
- Implement responsive design with mobile-first approach
- Follow accessibility best practices (ARIA labels, semantic elements)
- Use consistent naming conventions (BEM methodology preferred)
- Include proper meta tags and viewport configuration

### Design System Implementation:
- Typography: System fonts with proper hierarchy (2.5rem, 2rem, 1.5rem, 1.25rem, 1rem)
- Colors: Professional palette with primary (#2563eb), secondary (#10b981), neutral grays
- Spacing: Consistent 8px grid system (0.5rem, 1rem, 1.5rem, 2rem, 3rem, 4rem)
- Components: Clean buttons, cards, forms with subtle shadows and proper states
- Layout: CSS Grid for page structure, Flexbox for components

### Code Structure:
- Properly formatted and indented HTML
- Organized CSS with logical grouping (reset, variables, layout, components)
- Minimal, efficient JavaScript for interactions
- Comments for complex sections
- No external dependencies unless absolutely necessary

### Plan Implementation:
- Follow the plan specifications exactly
- Implement all features and sections mentioned in the plan
- Maintain consistency with the planned design approach
- Include proper error handling and user feedback where specified

Return ONLY the complete HTML document with embedded CSS and JavaScript. No markdown formatting, no explanations, just clean production code.`;

    const messages = [
      new SystemMessage(systemPrompt),
      new HumanMessage(`Implement this plan as a complete HTML document:\n\n${plan}`)
    ];

    try {
      this.sendSSEEvent(res, 'start', 'Starting code generation...');

      const stream = await llm.stream(messages);

      for await (const chunk of stream) {
        if (chunk.content) {
          this.sendSSEEvent(res, 'data', chunk.content);
        }
      }

      this.sendSSEEvent(res, 'end', 'Code generation completed');
      res.end();
    } catch (error) {
      console.error('Error generating code:', error);
      this.sendSSEEvent(res, 'error', error.message);
      res.end();
    }
  }
}

module.exports = new LLMServiceV3();
