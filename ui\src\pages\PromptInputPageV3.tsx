import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FiS<PERSON>, <PERSON><PERSON><PERSON>der, FiMonitor, FiSmartphone } from 'react-icons/fi';

/**
 * PromptInputPageV3 - Readdy.ai style initial prompt page
 * Clean, centered design with device selection
 */

type DeviceType = 'desktop' | 'mobile';

export function PromptInputPageV3() {
  const [prompt, setPrompt] = useState('');
  const [deviceType, setDeviceType] = useState<DeviceType>('desktop');
  const [isGenerating, setIsGenerating] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!prompt.trim() || isGenerating) return;

    setIsGenerating(true);

    try {
      // Navigate to plan review page with the prompt
      navigate('/plan-v3', {
        state: {
          prompt: prompt.trim(),
          deviceType
        }
      });
    } catch (error) {
      console.error('Error:', error);
      setIsGenerating(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="w-full max-w-2xl">
        {/* Logo and Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-6">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center">
              <span className="text-white font-bold text-xl">JP</span>
            </div>
            <span className="ml-3 text-2xl font-bold text-gray-900">JustPrototype</span>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            What would you like to design today?
          </h1>
          <p className="text-lg text-gray-600">
            Describe your vision and I'll create it for you
          </p>
        </div>

        {/* Device Type Selection */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-lg p-1 shadow-sm border border-gray-200">
            <button
              type="button"
              onClick={() => setDeviceType('desktop')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                deviceType === 'desktop'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <FiMonitor />
              <span>Desktop</span>
            </button>
            <button
              type="button"
              onClick={() => setDeviceType('mobile')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                deviceType === 'mobile'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <FiSmartphone />
              <span>Mobile</span>
            </button>
          </div>
        </div>

        {/* Prompt Input */}
        <form onSubmit={handleSubmit} className="relative">
          <div className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
            <textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="Describe your needs for a single page...
Include style, features, or purpose for best results.
Reference images also welcome ✨"
              className="w-full h-32 px-6 py-4 text-gray-900 placeholder-gray-500 border-0 resize-none focus:outline-none focus:ring-0"
              disabled={isGenerating}
            />
            
            {/* Input Actions */}
            <div className="flex items-center justify-between px-6 py-4 bg-gray-50 border-t border-gray-100">
              <div className="flex items-center space-x-3">
                <button
                  type="button"
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                  title="Add attachment"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                </button>
                <button
                  type="button"
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                  title="Color palette"
                >
                  <div className="w-5 h-5 rounded-full bg-gradient-to-r from-red-400 via-yellow-400 to-blue-400"></div>
                </button>
                <button
                  type="button"
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                  title="Style options"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
                  </svg>
                </button>
                <button
                  type="button"
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                  title="Voice input"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                  </svg>
                </button>
                <button
                  type="button"
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                  title="Image upload"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </button>
              </div>
              
              <button
                type="submit"
                disabled={!prompt.trim() || isGenerating}
                className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isGenerating ? (
                  <>
                    <FiLoader className="animate-spin" />
                    <span>Creating...</span>
                  </>
                ) : (
                  <>
                    <FiSend />
                    <span>Create</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </form>

        {/* Examples */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500 mb-4">Try these examples:</p>
          <div className="flex flex-wrap justify-center gap-2">
            {[
              'Modern landing page for a coffee shop',
              'Dashboard for project management',
              'E-commerce product page',
              'Portfolio website for photographer'
            ].map((example, index) => (
              <button
                key={index}
                onClick={() => setPrompt(example)}
                className="px-3 py-1 text-sm text-blue-600 bg-blue-50 rounded-full hover:bg-blue-100 transition-colors"
                disabled={isGenerating}
              >
                {example}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
