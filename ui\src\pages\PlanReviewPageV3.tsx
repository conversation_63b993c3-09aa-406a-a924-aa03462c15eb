import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FiArrowRight } from 'react-icons/fi';

/**
 * PlanReviewPageV3 - Readdy.ai style plan generation and review
 * Clean, focused plan view without conversation sidebar
 */

interface PlanItem {
  title: string;
  description: string;
  details: string[];
}

interface GeneratedPlan {
  overview: string;
  sections: PlanItem[];
  features: string[];
  accessibility: string[];
}

export function PlanReviewPageV3() {
  const location = useLocation();
  const navigate = useNavigate();
  const { prompt, deviceType } = location.state || {};

  const [plan, setPlan] = useState<GeneratedPlan | null>(null);
  const [isGeneratingPlan, setIsGeneratingPlan] = useState(true);
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);

  useEffect(() => {
    if (!prompt) {
      navigate('/prompt-v3');
      return;
    }
    generatePlan();
  }, [prompt, navigate]);

  const generatePlan = async () => {
    try {
      setIsGeneratingPlan(true);

      const response = await fetch('/api/llm/v3/plan', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt,
          deviceType: deviceType || 'desktop'
        })
      });

      if (!response.ok) throw new Error('Failed to generate plan');

      const data = await response.json();
      setPlan(data.plan);


    } catch (error) {
      console.error('Error generating plan:', error);
      // Fallback plan for demo
      setPlan({
        overview: "The login page features a clean, minimalist design with a centered content area against a neutral background. The layout is vertically aligned to create a clear visual hierarchy.",
        sections: [
          {
            title: "Overall Layout",
            description: "Clean, centered design with neutral background",
            details: [
              "Centered content area against neutral background",
              "Vertically aligned layout for clear visual hierarchy",
              "Responsive design that adapts to different screen sizes"
            ]
          },
          {
            title: "Header Section",
            description: "Company branding and welcome message",
            details: [
              "Company logo or app name positioned at the top",
              "Subtitle or tagline below the logo",
              "Welcome message or tagline below the logo",
              "Clear spacing between header and form elements"
            ]
          },
          {
            title: "Login Form",
            description: "User authentication interface",
            details: [
              "Email/Username input field with appropriate icon and placeholder text",
              "Password input field with visibility toggle icon",
              "Forgot Password link aligned to the right below password field",
              "Primary Login button spanning full width of the form",
              "Button features a solid background color with contrasting text"
            ]
          },
          {
            title: "Alternative Login Options",
            description: "Social authentication methods",
            details: [
              "Horizontal divider with OR text in the center",
              "Social login buttons for Google and Apple",
              "Icons aligned with text for better visual hierarchy"
            ]
          }
        ],
        features: [
          "Consistent padding and margins throughout",
          "Subtle shadows for depth",
          "Input fields with clear focus states",
          "Responsive design that adapts to different screen sizes",
          "Error state handling for form validation"
        ],
        accessibility: [
          "Clear focus indicators",
          "Proper form labeling",
          "Sufficient color contrast",
          "Error messages for screen readers"
        ]
      });
    } finally {
      setIsGeneratingPlan(false);
    }
  };

  const handleGenerate = async () => {
    setIsGeneratingCode(true);

    try {
      // Navigate to editor with plan and prompt
      navigate('/editor-v3', {
        state: {
          prompt,
          deviceType,
          plan,
          initialGeneration: true
        }
      });
    } catch (error) {
      console.error('Error:', error);
      setIsGeneratingCode(false);
    }
  };



  if (!prompt) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-6xl mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Design Plan</h1>
              <p className="text-gray-600 mt-2">Review the detailed implementation plan</p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                {deviceType === 'desktop' ? '🖥️ Desktop' : '📱 Mobile'}
              </span>
              <button
                onClick={handleGenerate}
                disabled={isGeneratingPlan || isGeneratingCode}
                className="flex items-center space-x-2 px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
              >
                {isGeneratingCode ? (
                  <>
                    <FiLoader className="animate-spin" />
                    <span>Generating...</span>
                  </>
                ) : (
                  <>
                    <span>Generate</span>
                    <FiArrowRight />
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-5xl mx-auto px-6 py-8">
        {isGeneratingPlan ? (
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <div className="relative">
                <div className="w-20 h-20 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-6"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-8 h-8 bg-blue-600 rounded-full animate-pulse"></div>
                </div>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Creating your design plan</h3>
              <p className="text-gray-600">Analyzing your requirements and crafting a detailed implementation strategy...</p>
            </div>
          </div>
        ) : plan ? (
          <div className="space-y-12">
            {/* Overview Card */}
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-100">
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center flex-shrink-0">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-gray-900 mb-3">Project Overview</h3>
                  <p className="text-gray-700 text-lg leading-relaxed">{plan.overview}</p>
                </div>
              </div>
            </div>

            {/* Implementation Sections */}
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <span className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center mr-3">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </span>
                Implementation Sections
              </h3>
              <div className="grid gap-6">
                {plan.sections.map((section, index) => (
                  <div key={index} className="bg-white rounded-2xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow overflow-hidden">
                    <div className="p-6">
                      <div className="flex items-start space-x-4">
                        <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 text-white rounded-xl flex items-center justify-center text-lg font-bold">
                          {index + 1}
                        </div>
                        <div className="flex-1">
                          <h4 className="text-xl font-semibold text-gray-900 mb-2">{section.title}</h4>
                          <p className="text-gray-600 mb-4 text-lg">{section.description}</p>
                          <div className="space-y-3">
                            {section.details.map((detail, detailIndex) => (
                              <div key={detailIndex} className="flex items-start space-x-3">
                                <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                  <FiCheck className="text-green-600 w-3 h-3" />
                                </div>
                                <span className="text-gray-700 leading-relaxed">{detail}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Features & Accessibility Grid */}
            <div className="grid md:grid-cols-2 gap-8">
              {/* Features */}
              <div className="bg-white rounded-2xl border border-gray-200 shadow-sm p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                  <span className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center mr-3">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </span>
                  Key Features
                </h3>
                <div className="space-y-3">
                  {plan.features.map((feature, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <div className="w-4 h-4 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <FiCheck className="text-green-600 w-3 h-3" />
                      </div>
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Accessibility */}
              <div className="bg-white rounded-2xl border border-gray-200 shadow-sm p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                  <span className="w-8 h-8 bg-orange-600 rounded-lg flex items-center justify-center mr-3">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  </span>
                  Accessibility
                </h3>
                <div className="space-y-3">
                  {plan.accessibility.map((item, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <div className="w-4 h-4 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <FiCheck className="text-orange-600 w-3 h-3" />
                      </div>
                      <span className="text-gray-700">{item}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Failed to generate plan</h3>
            <p className="text-gray-600 mb-4">Something went wrong while creating your design plan.</p>
            <button
              onClick={() => window.location.reload()}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
